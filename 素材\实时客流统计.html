<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <title>实时客流统计</title>
  <script src="https://cdn.jsdelivr.net/npm/echarts@5.5.0/dist/echarts.min.js"></script>
  <style>
    body {
      margin: 0;
      background-color: #0f2747;
    }
    #main {
      width: 50vw;
      height: 50vh;
    }
  </style>
</head>
<body>
  <div id="main"></div>
  <script>
    const chartDom = document.getElementById('main');
    const myChart = echarts.init(chartDom);
    
    const option = {
      backgroundColor: '#0f2747',
      title: {
        left: 'left',
        top: '10px',
        textStyle: {
          color: '#ffffff',
          fontSize: 20
        }
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: Array.from({length: 60}, (_, i) => i),
        axisLine: { lineStyle: { color: '#ffffff33' } },
        axisLabel: { color: '#ccc' }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 10,
        axisLine: { lineStyle: { color: '#ffffff33' } },
        splitLine: { lineStyle: { color: '#ffffff22' } },
        axisLabel: { color: '#ccc' }
      },
      grid: {
        top: '10%',
        containLabel: true
      },
      series: [
        {
          name: '客流量',
          type: 'line',
          data: Array.from({length: 60}, () => Math.floor(Math.random() * 10)),
          smooth: 1,
          symbol: 'none',
          lineStyle: {
            color: '#00cfff',
            width: 2
          },
          areaStyle: {
            color: 'rgba(0, 207, 255, 0.1)'
          }
        }
      ]
    };

    myChart.setOption(option);
  </script>
</body>
</html>
