<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>地图放大缩小按钮</title>
<style>
.zoom-button {
  width: 40px;
  height: 40px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease-in-out; /* 添加过渡效果 */
}

.zoom-button:hover {
  transform: scale(1.1); /* 鼠标悬停时放大 */
}

.zoom-button:active {
  transform: scale(0.9); /* 点击时缩小 */
}

.zoom-button::before {
  content: '+'; /* 加号图标 */
  font-size: 20px;
  color: #333;
}

.zoom-button.zoom-out::before {
  content: '-'; /* 减号图标 */
}

.zoom-button.zoom-out:hover {
  transform: scale(1.1);
}
.zoom-button.zoom-out:active {
  transform: scale(0.9);
}
</style>
</head>
<body>

<div class="zoom-button"></div>
<div class="zoom-button zoom-out"></div>

</body>
</html>