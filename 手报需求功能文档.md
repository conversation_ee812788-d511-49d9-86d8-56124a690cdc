# 手报需求功能文档

## 项目概述

基于现有的智能疏散指示系统，集成手动报警器功能，实现统一的报警状态管理和实时监控。

## 功能需求

### 1. 建筑状态显示改进 (`BuildingStatus.vue`)

#### 1.1 状态标题保持
- 标题：`建筑状态`（保持不变）

#### 1.2 状态内容适配
**状态圆圈显示**：
- `正常状态` - 绿色圆圈，当 `alert: false` 时
- `紧急状态` - 红色圆圈，当 `alert: true` 时

**状态描述文字**：
- 正常时：`系统运行正常`
- 报警时：`手动报警器已触发`

### 2. 引导员管理改进 (`GuideManagement.vue`)

#### 2.1 紧急按钮功能变更
- 按钮标题：保持 `紧急`
- 功能变更：从引导员紧急呼叫 → 手动报警器触发按钮
- 点击后：发送POST请求到 `http://************:5005`

#### 2.2 数据格式
```json
{
    "message": "手动报警器触发",
    "recipients": ["2015559", "2515523"],
    "alert": true
}
```

### 3. 实时状态上报改进 (`StatusReport.vue`)

#### 3.1 移除模拟数据
- 删除所有现有的模拟上报记录
- 初始状态为空表格

#### 3.2 报警上报功能
**触发时机**：点击"紧急"按钮成功后自动添加记录

**上报内容选择**：
- `火灾`
- `人群骚乱`
- `地震`
- `袭击事件`
- `自定义内容`（提供输入框）

**记录格式**：
```javascript
{
    time: '2024/01/15/14:30:25',
    reporter: 'admin',  // 当前用户
    content: '选择的报警类型或自定义内容'
}
```

### 4. API集成方案

#### 4.1 接口地址
- **触发报警**：`POST http://************:5005`
- **查询状态**：`GET http://************:5005/status`

#### 4.2 状态轮询策略
- **正常状态**：每30秒轮询一次
- **报警状态**：每5秒轮询一次（监控状态恢复）
- **手动触发**：点击按钮后立即查询

#### 4.3 跨域处理
在 `vite.config.js` 中配置代理：
```javascript
proxy: {
  '/api': {
    target: 'http://************:5005',
    changeOrigin: true,
    secure: false,
  }
}
```

## 技术实现

### 1. 组件修改清单

#### 1.1 `BuildingStatus.vue`
- 添加API状态查询功能
- 实现状态轮询机制
- 根据 `alert` 字段动态更新显示

#### 1.2 `GuideManagement.vue`
- 修改"紧急"按钮点击事件
- 添加报警类型选择弹窗
- 实现POST请求发送

#### 1.3 `StatusReport.vue`
- 清空模拟数据
- 添加动态记录添加功能
- 实现报警记录管理

#### 1.4 `App.vue`
- 添加全局状态管理
- 实现组件间数据传递
- 统一错误处理

### 2. 状态管理流程

```
用户点击"紧急"按钮 
    ↓
选择报警类型
    ↓
发送POST请求
    ↓
查询GET状态
    ↓
更新建筑状态显示
    ↓
添加上报记录
    ↓
开始高频轮询(5秒)
    ↓
检测到状态恢复
    ↓
更新为正常状态
    ↓
恢复低频轮询(30秒)
```

### 3. 错误处理

- **网络错误**：显示连接失败提示
- **API错误**：记录错误日志
- **超时处理**：自动重试机制

## 测试验证

### 1. 功能测试
- 使用 `发送测试灯牌和get.py` 验证API连通性
- 测试状态切换的实时性
- 验证轮询机制的准确性

### 2. 界面测试
- 确认状态显示的视觉效果
- 验证报警类型选择功能
- 测试上报记录的正确性

## 部署说明

### 1. 开发环境
```bash
npm run dev
# 访问: http://localhost:3000
```

### 2. 生产环境
- 确保API服务 `http://************:5005` 可访问
- 配置正确的代理设置
- 测试跨域请求正常

## 注意事项

1. **API依赖**：功能依赖阿里云上的 `get_message.py` 服务
2. **网络要求**：需要能访问 `************:5005` 端口
3. **状态同步**：轮询频率需要平衡实时性和性能
4. **用户体验**：报警操作需要明确的确认机制
5. **数据持久化**：上报记录仅在前端保存，刷新后丢失

## 后续扩展

1. **历史记录**：添加报警历史查询功能
2. **通知机制**：集成浏览器通知API
3. **权限管理**：添加操作权限验证
4. **数据统计**：报警频率和类型统计