<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <title>疏散模拟结果</title>
  <script src="https://cdn.jsdelivr.net/npm/echarts@5.5.0/dist/echarts.min.js"></script>
  <style>
    body {
      margin: 0;
      background-color: #0f2747;
    }
    #main {
      width: 100vw;
      height: 100vh;
    }
  </style>
</head>
<body>
  <div id="main"></div>
  <script>
    const chartDom = document.getElementById('main');
    const myChart = echarts.init(chartDom);

    const option = {
      backgroundColor: '#0f2747',
      title: {
        textStyle: {
          color: '#ffffff',
          fontSize: 24,
          fontWeight: 'bold'
        }
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: Array.from({ length: 61 }, (_, i) => i),
        axisLine: { lineStyle: { color: '#ffffff33' } },
        axisLabel: {
          color: '#ccc',
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 10,
        axisLine: { lineStyle: { color: '#ffffff33' } },
        splitLine: { lineStyle: { color: '#ffffff22' } },
        axisLabel: {
          show: false
        }
      },
      grid: {
        top: '10%',
        bottom: '5px',
        containLabel: true
      },
      series: [
        {
          name: '疏散人数',
          type: 'line',
          data: [], // 初始无数据
          smooth: 0.9,
          symbol: 'none',
          lineStyle: {
            color: '#00cfff',
            width: 3,
            shadowColor: 'rgba(0, 207, 255, 0.5)',
            shadowBlur: 4
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(0, 207, 255, 0.4)' },
              { offset: 1, color: 'rgba(0, 207, 255, 0)' }
            ])
          }
        }
      ]
    };

    myChart.setOption(option);
  </script>
</body>
</html>
