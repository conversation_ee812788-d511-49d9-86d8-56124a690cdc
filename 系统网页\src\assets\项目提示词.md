按照图片作为格式参考，进行前端的界面样式的复现，无需考虑背后的接口功能，和数据交互

# 智能疏散指示系统前端界面

必须使用Vue3架构进行处理

基于Vue3的智能疏散指示系统前端界面实现，主要复现了系统的UI样式。

## 功能说明

本项目主要实现了智能疏散指示系统的前端界面，包括：

顶部（按照图片样式显示：1.”智能疏散指示系统“”@上海理工大学“）（”陕西南路站“）（时间显示实时的系统时间）（admin，是用户名，默认admin），（接着是退出按钮，关闭页面使用）

左侧
1. 建筑状态显示（按照图片显示一样，以复现为前提，默认一个圆形与圆形中有字样（例如图中是红色的圆中”密度过高“），底下的字为：”当前状态...“，注意样式格式排版）
2. 实时状态上报图表（位于建筑状态显示下方，三个部分：告警时间，上报人员，上报内容，浅白半透明为表格头部）
3. 疏散仿真与优化（三个按钮：疏散仿真，路径优化，指示牌更新;样式以图片为准,蓝色长条按钮，按钮文字居中显示）

中间
4. 地图
-（内容框内左上角四个蓝色按钮，人员热力图，引导员位置，疏散路线，指示标识，以浮动的方式，在地图中的地图模型显示）
-（本质上像是谷歌地图一样的显示逻辑，但是目前只要地图模型.png进行显示即可，不需要进行交互）
-（**注意，中间的具体地图图片忽略，那个地图模型无需考虑，不作为样式设计，在内容区插入该“地图模型.png”图片进行内容复现即可）
-（注意右边按钮样式设计，顶部一个小按钮的3D模式切换使用”3D小按钮.png“插入即可，然后是加减号，同样需要B1 M B2等等按钮，那个是地图模型自带数据，仅做到样式复现即可）
-（加减号按钮真需要做到图片放大缩小功能）

5. 疏散模拟结果（位于地图下方，推断是柱状图，但没有数据，简单设计成空的柱状图形式即可）
6. 实时客流统计（位于疏散模拟结果的右边，进行内容样式复现）

右侧
7.视频监控（整个项目，未完成部分，仅留个内容框即可）
8.指示牌管理（按照图片显示：内容区最左侧为一个框，插入图片”指示牌D001.png“即可，接着按照图片显示，设计其他内容，”欢迎使用灯牌系统“，底下左边一个状态显示圆（默认灰色），右边两个按钮）
9.引导员管理（在内容区内，左侧插入图片”引导员D001.png“，其余内容按照图片显示；注意底下两个按钮设计）

## 注意事项

- 本项目仅实现了前端UI界面，暂未实现后端接口和数据交互
- 地图和图表使用模拟数据
- 必须，所有功能都显示在整个界面上，即排版大小设计要符合自动匹配！
- 界面基于Vue3，其他按需引入构建