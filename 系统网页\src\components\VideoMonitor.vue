<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';

// 视频设备WebSDK全局变量
let WebVideoCtrl = null;
let g_iWndIndex = 0; // 当前选中窗口

// 视频监控状态
const videoStatus = ref({
  isConnected: false,
  isPlaying: false,
  isLoggedIn: false,
  isLogging: false,
  currentCamera: 1,
  quality: 'HD',
  deviceIP: '***********',
  devicePort: 80,
  username: 'admin',
  password: 'hucom12345',
  protocolType: 1, // 1=HTTP, 2=HTTPS
  statusMessage: '请输入设备信息并登录',
  showStatusMessage: false // 初始状态不显示，等待用户操作后再显示
});

// 状态消息自动隐藏定时器
let statusMessageTimer = null;

// 设置状态消息并启动自动隐藏定时器
const setStatusMessage = (message, autoHide = true) => {
  videoStatus.value.statusMessage = message;
  videoStatus.value.showStatusMessage = true;

  // 清除之前的定时器
  if (statusMessageTimer) {
    clearTimeout(statusMessageTimer);
    statusMessageTimer = null;
  }

  // 如果需要自动隐藏，设置10秒后隐藏
  if (autoHide) {
    statusMessageTimer = setTimeout(() => {
      videoStatus.value.showStatusMessage = false;
      statusMessageTimer = null;
    }, 10000); // 10秒后隐藏
  }
};

// 实时人数统计状态
const personCount = ref({
  current: 0,
  isEnabled: false,
  isConnecting: false,
  lastUpdate: null,
  apiUrl: 'http://localhost:5123',
  status: 'stopped'
});

// 操作日志
const operationLogs = ref([]);
const maxLogs = 50; // 最多保留50条日志

// 登录超时控制
let loginTimeout = null;

// 插件状态
const pluginStatus = ref({
  isInstalled: false,
  isInitialized: false,
  version: '',
  needUpdate: false
});

// 摄像头通道列表（动态获取）
const cameras = ref([]);

// 设备标识
const deviceIdentify = ref('');

// 控制高级配置显示
const showAdvancedConfig = ref(false);

// 连接状态检测定时器
let connectionCheckTimer = null;

// 实时人数统计定时器
let personCountTimer = null;

// 添加操作日志
const addLog = (message, type = 'info') => {
  const timestamp = new Date().toLocaleTimeString();
  const log = {
    id: Date.now(),
    timestamp,
    message,
    type // info, success, error, warning
  };

  operationLogs.value.unshift(log);

  // 限制日志数量
  if (operationLogs.value.length > maxLogs) {
    operationLogs.value = operationLogs.value.slice(0, maxLogs);
  }

  // 同时更新状态消息
  setStatusMessage(message);

  // 控制台输出
  console.log(`[${timestamp}] ${message}`);
};

// 清空日志
const clearLogs = () => {
  operationLogs.value = [];
};

// 构建RTSP URL
const buildRtspUrl = () => {
  const { deviceIP, username, password, currentCamera } = videoStatus.value;
  // 根据海康威视RTSP URL格式构建
  // rtsp://用户名:密码@ip:端口/Streaming/Channels/通道号+0+码流代号
  // 例如：第1通道主码流=101，第12通道主码流=1201
  const channelId = currentCamera.toString() + '0' + '1'; // 通道号+0+1(主码流)
  return `rtsp://${username}:${password}@${deviceIP}:554/Streaming/Channels/${channelId}`;
};

// 获取实时人数统计
const getPersonCount = async () => {
  if (!personCount.value.isEnabled) return;

  try {
    const response = await fetch(`${personCount.value.apiUrl}/api/count`);
    if (response.ok) {
      const data = await response.json();
      personCount.value.current = data.count || 0;
      personCount.value.status = data.status || 'unknown';
      personCount.value.lastUpdate = new Date().toLocaleTimeString();
    } else {
      console.error('获取实时人数统计失败:', response.status);
    }
  } catch (error) {
    console.error('实时人数统计API连接失败:', error);
    personCount.value.status = 'error';
  }
};

// 设置RTSP URL到后端API
const setRtspUrlToApi = async (rtspUrl) => {
  if (!personCount.value.isEnabled) return;

  try {
    personCount.value.isConnecting = true;
    addLog(`正在设置人员识别视频源: ${rtspUrl}`, 'info');

    const response = await fetch(`${personCount.value.apiUrl}/api/set_rtsp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ rtsp_url: rtspUrl })
    });

    if (response.ok) {
      const data = await response.json();
      addLog('人员识别视频源设置成功', 'success');
      personCount.value.status = data.status || 'running';

      // 开始定期获取实时人数统计
      startPersonCountTimer();
    } else {
      addLog('设置人员识别视频源失败', 'error');
      personCount.value.status = 'error';
    }
  } catch (error) {
    console.error('设置RTSP URL失败:', error);
    addLog('人员识别API连接失败', 'error');
    personCount.value.status = 'error';
  } finally {
    personCount.value.isConnecting = false;
  }
};

// 启动实时人数统计定时器
const startPersonCountTimer = () => {
  if (personCountTimer) {
    clearInterval(personCountTimer);
  }

  // 每1秒获取一次实时人数统计
  personCountTimer = setInterval(() => {
    getPersonCount();
  }, 1000);

  // 立即获取一次
  getPersonCount();
};

// 停止实时人数统计定时器
const stopPersonCountTimer = () => {
  if (personCountTimer) {
    clearInterval(personCountTimer);
    personCountTimer = null;
  }
};

// 切换实时人数统计功能
const togglePersonCount = () => {
  personCount.value.isEnabled = !personCount.value.isEnabled;

  if (personCount.value.isEnabled) {
    addLog('实时人数统计功能已启用', 'success');
    if (videoStatus.value.isPlaying) {
      const rtspUrl = buildRtspUrl();
      setRtspUrlToApi(rtspUrl);
    }
  } else {
    addLog('实时人数统计功能已禁用', 'info');
    stopPersonCountTimer();
    personCount.value.current = 0;
    personCount.value.status = 'stopped';
  }
};

// 检查插件安装状态
const checkPluginInstallation = () => {
  return new Promise((resolve, reject) => {
    // 检查WebVideoCtrl是否存在
    if (typeof window.WebVideoCtrl === 'undefined') {
      addLog('WebSDK未加载', 'error');
      reject(new Error('WebSDK未加载'));
      return;
    }

    // 检查插件是否安装
    try {
      // 尝试创建插件实例来检查是否安装
      const testElement = document.createElement('div');
      testElement.id = 'pluginTest';
      testElement.style.display = 'none';
      document.body.appendChild(testElement);

      WebVideoCtrl.I_InsertOBJECTPlugin('pluginTest').then(() => {
        addLog('插件安装检查通过', 'success');
        pluginStatus.value.isInstalled = true;
        document.body.removeChild(testElement);
        resolve(true);
      }).catch(() => {
        addLog('插件未安装或不兼容', 'error');
        pluginStatus.value.isInstalled = false;
        document.body.removeChild(testElement);
        reject(new Error('插件未安装'));
      });

    } catch (error) {
      addLog('插件检查异常', 'error');
      reject(error);
    }
  });
};

// 初始化视频设备WebSDK
const initWebSDK = async () => {
  try {
    addLog('正在初始化视频插件...', 'info');

    // 检查jQuery是否已加载
    if (typeof window.$ === 'undefined') {
      addLog('jQuery未加载，正在加载...', 'warning');
      await loadScript('/hikvision/jquery-1.7.1.min.js');
      addLog('jQuery加载完成', 'success');
    }

    // 动态加载WebSDK脚本
    if (typeof window.WebVideoCtrl === 'undefined') {
      addLog('正在加载WebSDK核心文件...', 'info');
      await loadScript('/hikvision/webVideoCtrl.js');
      await loadScript('/hikvision/jsVideoPlugin-1.0.0.min.js');
      addLog('WebSDK文件加载完成', 'success');
    }

    if (window.WebVideoCtrl) {
      WebVideoCtrl = window.WebVideoCtrl;
      addLog('WebSDK加载成功，正在检查插件安装...', 'info');

      // 先检查插件是否安装
      try {
        await checkPluginInstallation();
        addLog('插件检查通过，正在初始化...', 'success');
      } catch (error) {
        addLog('插件检查失败，尝试继续初始化...', 'warning');
      }

      // 初始化插件
      WebVideoCtrl.I_InitPlugin({
        bWndFull: true, // 支持单窗口双击全屏
        iWndowType: 1,  // 1x1窗口
        cbSelWnd: function (xmlDoc) {
          g_iWndIndex = parseInt($(xmlDoc).find("SelectWnd").eq(0).text(), 10);
          addLog(`当前选择的窗口编号：${g_iWndIndex}`, 'info');
        },
        cbDoubleClickWnd: function (iWndIndex, bFullScreen) {
          const action = bFullScreen ? "全屏" : "还原";
          addLog(`窗口${iWndIndex}${action}`, 'info');
        },
        cbEvent: function (iEventType, iParam1, iParam2) {
          if (2 == iEventType) {
            addLog(`窗口${iParam1}回放结束`, 'info');
          } else if (-1 == iEventType) {
            addLog(`设备${iParam1}网络错误`, 'error');
            emit('video-error', { message: '网络错误' });
          }
        },
        cbInitPluginComplete: function () {
          addLog('视频插件初始化完成', 'success');
          pluginStatus.value.isInitialized = true;
          initVideoContainer().then(() => {
            // 插件和容器都初始化完成后，如果设置了自动连接，则开始连接
            if (props.autoConnect && !videoStatus.value.isLoggedIn) {
              addLog('开始自动连接视频设备...', 'info');
              setTimeout(connectVideo, 1000);
            }
          });
        }
      });
    } else {
      throw new Error('WebVideoCtrl未定义');
    }
  } catch (error) {
    addLog(`WebSDK初始化失败: ${error.message}`, 'error');
    addLog('请检查：1. 插件是否安装 2. 浏览器是否兼容 3. 网络连接', 'info');
    emit('video-error', error);
  }
};

// 初始化视频容器
const initVideoContainer = async () => {
  try {
    addLog('正在初始化视频容器...', 'info');

    // 等待DOM更新完成
    await nextTick();

    // 等待一小段时间确保容器完全渲染
    await new Promise(resolve => setTimeout(resolve, 100));

    const videoContainer = document.getElementById('videoContainer');
    if (videoContainer) {
      // 设置容器的基本样式，确保插件能正确渲染
      videoContainer.style.width = '100%';
      videoContainer.style.height = '100%';
      videoContainer.style.position = 'absolute';
      videoContainer.style.overflow = 'hidden';
      addLog('视频容器DOM元素准备完成', 'success');
    } else {
      addLog('视频容器DOM元素未找到，但继续初始化插件', 'warning');
    }

    // 使用Promise方式初始化插件容器
    await new Promise((resolve, reject) => {
      WebVideoCtrl.I_InsertOBJECTPlugin("videoContainer").then(() => {
        addLog('视频容器初始化成功', 'success');

        // 检查插件版本
        WebVideoCtrl.I_CheckPluginVersion().then((bNeedUpdate) => {
          if (bNeedUpdate) {
            addLog('检测到新的插件版本，建议升级插件', 'warning');
            addLog('请运行 HCWebSDKPluginsUserSetup.exe 升级插件', 'info');
          } else {
            addLog('插件版本检查完成，版本为最新', 'success');
          }

          // 设置插件窗口大小
          setTimeout(() => {
            const container = document.getElementById('videoContainer');
            if (container && WebVideoCtrl) {
              WebVideoCtrl.I_Resize(container.offsetWidth, container.offsetHeight);
              addLog(`视频窗口大小已设置: ${container.offsetWidth}x${container.offsetHeight}`, 'info');
            }
            resolve();
          }, 100);
        }).catch(() => {
          addLog('插件版本检查失败，但容器已初始化', 'warning');
          // 仍然设置窗口大小
          setTimeout(() => {
            const container = document.getElementById('videoContainer');
            if (container && WebVideoCtrl) {
              WebVideoCtrl.I_Resize(container.offsetWidth, container.offsetHeight);
            }
            resolve();
          }, 100);
        });

      }).catch((error) => {
        addLog('插件初始化失败，请检查插件安装', 'error');
        addLog('如果未安装插件，请运行 HCWebSDKPluginsUserSetup.exe', 'error');
        reject(new Error('插件未安装或初始化失败'));
      });
    });

  } catch (error) {
    addLog(`视频容器初始化失败: ${error.message}`, 'error');
    addLog('解决方案：1. 安装插件 2. 刷新页面 3. 检查浏览器兼容性', 'info');
    emit('video-error', error);
  }
};

// 检查视频容器状态
const checkVideoContainerStatus = () => {
  const videoContainer = document.getElementById('videoContainer');
  if (!videoContainer) {
    addLog('视频容器DOM元素未找到', 'error');
    return false;
  }

  // 检查容器的子元素
  const children = Array.from(videoContainer.children);
  const videoElements = children.filter(child =>
    child.tagName === 'OBJECT' ||
    child.tagName === 'EMBED' ||
    child.classList.contains('video-plugin') ||
    child.id.includes('plugin')
  );

  addLog(`视频容器状态检查:`, 'info');
  addLog(`- 容器尺寸: ${videoContainer.offsetWidth}x${videoContainer.offsetHeight}`, 'info');
  addLog(`- 子元素数量: ${children.length}`, 'info');
  addLog(`- 视频插件元素: ${videoElements.length}`, 'info');

  if (videoElements.length > 0) {
    videoElements.forEach((element, index) => {
      addLog(`- 插件元素${index + 1}: ${element.tagName} (${element.offsetWidth}x${element.offsetHeight})`, 'info');
    });
    return true;
  }

  return false;
};

// 启动连接状态检测
const startConnectionCheck = () => {
  // 清除之前的定时器
  if (connectionCheckTimer) {
    clearInterval(connectionCheckTimer);
  }

  let checkCount = 0;
  const maxChecks = 20; // 最多检测20次（10秒）

  connectionCheckTimer = setInterval(() => {
    checkCount++;

    if (videoStatus.value.isPlaying) {
      // 检查视频容器是否有内容
      const hasVideoContent = checkVideoContainerStatus();

      if (hasVideoContent) {
        setStatusMessage(`✅ 视频连接成功 - 画面正常显示`);
        addLog('✅ 视频内容已在小窗口中正常显示', 'success');
        clearInterval(connectionCheckTimer);
        return;
      }

      // 每5次检查输出一次状态
      if (checkCount % 5 === 0) {
        addLog(`正在等待视频内容加载... (${checkCount}/${maxChecks})`, 'info');
      }
    }

    if (checkCount >= maxChecks) {
      if (videoStatus.value.isPlaying) {
        setStatusMessage(`⚠️ 视频预览已启动，但画面可能需要等待更长时间`);
        addLog('⚠️ 视频预览已启动，但画面加载时间较长，请检查网络连接', 'warning');
      }
      clearInterval(connectionCheckTimer);
    }
  }, 500);
};

// 停止连接状态检测
const stopConnectionCheck = () => {
  if (connectionCheckTimer) {
    clearInterval(connectionCheckTimer);
    connectionCheckTimer = null;
  }
};

// 动态加载脚本
const loadScript = (src) => {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = src;
    script.onload = resolve;
    script.onerror = reject;
    document.head.appendChild(script);
  });
};

// 取消登录
const cancelLogin = () => {
  if (loginTimeout) {
    clearTimeout(loginTimeout);
    loginTimeout = null;
  }

  videoStatus.value.isLogging = false;
  addLog('用户取消登录操作', 'warning');
};

// 登录设备
const loginDevice = async () => {
  if (!WebVideoCtrl) {
    addLog('WebSDK未初始化，请刷新页面重试', 'error');
    return;
  }

  if (!pluginStatus.value.isInitialized) {
    addLog('插件未初始化完成，请等待初始化完成', 'warning');
    return;
  }

  const { deviceIP, devicePort, username, password, protocolType } = videoStatus.value;

  // 验证输入
  if (!deviceIP.trim()) {
    addLog('请输入设备IP地址', 'warning');
    return;
  }

  if (!username.trim() || !password.trim()) {
    addLog('请输入用户名和密码', 'warning');
    return;
  }

  deviceIdentify.value = `${deviceIP}_${devicePort}`;
  videoStatus.value.isLogging = true;
  const protocolName = protocolType === 2 ? 'HTTPS' : 'HTTP';
  addLog(`正在通过${protocolName}连接设备 ${deviceIP}:${devicePort}...`, 'info');

  // 设置登录超时
  loginTimeout = setTimeout(() => {
    if (videoStatus.value.isLogging) {
      videoStatus.value.isLogging = false;
      addLog('登录超时，请检查设备网络连接', 'error');
    }
  }, 10000); // 10秒超时

  try {
    await WebVideoCtrl.I_Login(deviceIP, protocolType, devicePort, username, password, {
      timeout: 8000,
      success: function () {
        if (loginTimeout) {
          clearTimeout(loginTimeout);
          loginTimeout = null;
        }

        addLog(`设备 ${deviceIdentify.value} 登录成功`, 'success');
        videoStatus.value.isLoggedIn = true;
        videoStatus.value.isLogging = false;
        emit('video-connected', deviceIdentify.value);

        // 登录成功后获取通道信息
        setTimeout(() => {
          getChannelInfo();
        }, 500);
      },
      error: function (oError) {
        if (loginTimeout) {
          clearTimeout(loginTimeout);
          loginTimeout = null;
        }

        videoStatus.value.isLoggedIn = false;
        videoStatus.value.isLogging = false;

        // 根据错误码提供具体的错误信息
        let errorMessage = '登录失败';
        if (oError.errorCode === 2001) {
          errorMessage = '设备已登录，请先断开连接';
        } else if (oError.errorCode === 1001) {
          errorMessage = '网络连接失败，请检查设备IP和网络';
        } else if (oError.errorCode === 401) {
          errorMessage = '用户名或密码错误';
        } else if (oError.errorCode === 1000) {
          errorMessage = '设备连接超时，请检查设备状态';
        } else {
          errorMessage = `登录失败: ${oError.errorMsg || '未知错误'} (错误码: ${oError.errorCode})`;
        }

        addLog(errorMessage, 'error');
        emit('video-error', oError);
      }
    });
  } catch (error) {
    if (loginTimeout) {
      clearTimeout(loginTimeout);
      loginTimeout = null;
    }

    videoStatus.value.isLogging = false;
    addLog(`登录异常: ${error.message}`, 'error');
    emit('video-error', error);
  }
};

// 获取通道信息
const getChannelInfo = async () => {
  if (!WebVideoCtrl || !videoStatus.value.isLoggedIn) return;

  try {
    await WebVideoCtrl.I_GetAnalogChannelInfo(deviceIdentify.value, {
      success: function (xmlDoc) {
        console.log('获取通道信息成功');
        // 解析通道信息并更新cameras列表
        const channels = $(xmlDoc).find("VideoInputChannel");
        const newCameras = [];

        channels.each(function() {
          const id = parseInt($(this).find("id").text(), 10);
          const name = $(this).find("name").text() || `通道${id}`;
          newCameras.push({
            id: id,
            name: name,
            status: 'online'
          });
        });

        if (newCameras.length > 0) {
          cameras.value = newCameras;
          videoStatus.value.currentCamera = newCameras[0].id;
          addLog(`获取到${newCameras.length}个模拟通道，正在启动预览...`, 'success');

          // 自动开始预览第一个通道
          setTimeout(() => {
            startRealPlay();
          }, 500);
        } else {
          addLog('设备连接成功，但未找到可用的模拟通道', 'warning');
        }
      },
      error: function (oError) {
        console.error('获取通道信息失败:', oError);
        setStatusMessage('获取通道信息失败，但设备已连接');
        // 如果获取模拟通道失败，尝试获取数字通道
        getDigitalChannelInfo();
      }
    });
  } catch (error) {
    console.error('获取通道信息异常:', error);
    setStatusMessage('获取通道信息异常');
  }
};

// 获取数字通道信息（备用方案）
const getDigitalChannelInfo = async () => {
  try {
    await WebVideoCtrl.I_GetDigitalChannelInfo(deviceIdentify.value, {
      success: function (xmlDoc) {
        console.log('获取数字通道信息成功');
        const channels = $(xmlDoc).find("InputProxyChannelStatus");
        const newCameras = [];

        channels.each(function() {
          const id = parseInt($(this).find("id").text(), 10);
          const name = $(this).find("name").text() || `数字通道${id}`;
          const online = $(this).find("online").text() === 'true';

          if (online) {
            newCameras.push({
              id: id,
              name: name,
              status: 'online'
            });
          }
        });

        if (newCameras.length > 0) {
          cameras.value = newCameras;
          videoStatus.value.currentCamera = newCameras[0].id;
          setStatusMessage(`设备连接成功，共${newCameras.length}个数字通道，正在启动预览...`);

          // 自动开始预览第一个通道
          setTimeout(() => {
            startRealPlay();
          }, 500);
        } else {
          // 如果都没有通道，提供默认通道
          cameras.value = [{ id: 1, name: '通道1', status: 'online' }];
          setStatusMessage('设备连接成功，使用默认通道，正在启动预览...');

          // 自动开始预览默认通道
          setTimeout(() => {
            startRealPlay();
          }, 500);
        }
      },
      error: function (oError) {
        console.error('获取数字通道信息失败:', oError);
        // 提供默认通道
        cameras.value = [{ id: 1, name: '通道1', status: 'online' }];
        setStatusMessage('设备连接成功，使用默认通道，正在启动预览...');

        // 自动开始预览默认通道
        setTimeout(() => {
          startRealPlay();
        }, 500);
      }
    });
  } catch (error) {
    console.error('获取数字通道信息异常:', error);
    cameras.value = [{ id: 1, name: '通道1', status: 'online' }];
    setStatusMessage('设备连接成功，使用默认通道');
  }
};

// 开始预览
const startRealPlay = async () => {
  if (!WebVideoCtrl || !videoStatus.value.isLoggedIn) {
    setStatusMessage('请先登录设备');
    addLog('设备未登录，无法开始预览', 'error');
    return;
  }

  if (!pluginStatus.value.isInitialized) {
    setStatusMessage('插件未初始化完成，请等待');
    addLog('插件未初始化完成，无法开始预览', 'warning');
    return;
  }

  if (cameras.value.length === 0) {
    setStatusMessage('没有可用的视频通道');
    addLog('没有可用的视频通道', 'warning');
    return;
  }

  setStatusMessage('正在启动视频预览...');
  addLog(`正在启动通道${videoStatus.value.currentCamera}的视频预览...`, 'info');

  // 确保视频容器准备就绪
  await nextTick();
  const videoContainer = document.getElementById('videoContainer');

  // 调整容器大小
  if (WebVideoCtrl.I_Resize && videoContainer) {
    WebVideoCtrl.I_Resize(videoContainer.offsetWidth, videoContainer.offsetHeight);
    addLog(`调整视频窗口大小: ${videoContainer.offsetWidth}x${videoContainer.offsetHeight}`, 'info');
  }

  try {
    await WebVideoCtrl.I_StartRealPlay(deviceIdentify.value, {
      iStreamType: 1, // 主码流
      iChannelID: videoStatus.value.currentCamera,
      bZeroChannel: false,
      success: function () {
        console.log('开始预览成功');
        videoStatus.value.isConnected = true;
        videoStatus.value.isPlaying = true;
        const currentCameraName = cameras.value.find(cam => cam.id === videoStatus.value.currentCamera)?.name || '未知通道';
        setStatusMessage(`✅ 视频连接成功 - 正在播放: ${currentCameraName}`);
        addLog(`视频预览启动成功 - ${currentCameraName}`, 'success');
        emit('video-connected', videoStatus.value.currentCamera);

        // 确保视频内容正确显示在容器中
        setTimeout(() => {
          const container = document.getElementById('videoContainer');
          if (container) {
            // 检查容器是否有视频内容
            const hasVideoContent = container.children.length > 1 ||
                                   container.querySelector('object') ||
                                   container.querySelector('embed');

            if (hasVideoContent) {
              addLog('✅ 视频内容已成功加载到小窗口', 'success');
              console.log('🎥 视频预览已在小窗口中显示');
            } else {
              addLog('⚠️ 视频预览已启动，但内容可能需要几秒钟加载', 'warning');
            }
          }
        }, 1000);

        // 启动连接状态检测
        startConnectionCheck();

        // 如果启用了实时人数统计，设置RTSP URL到后端API
        if (personCount.value.isEnabled) {
          const rtspUrl = buildRtspUrl();
          setTimeout(() => {
            setRtspUrlToApi(rtspUrl);
          }, 1500); // 延迟1.5秒确保视频流稳定
        }
      },
      error: function (oError) {
        console.error('开始预览失败:', oError);
        videoStatus.value.isConnected = false;
        videoStatus.value.isPlaying = false;

        let errorMessage = '❌ 视频预览失败';
        if (oError.errorCode === 3001) {
          errorMessage = '❌ 当前窗口已在播放，请先停止';
        } else if (oError.errorCode === 3000) {
          errorMessage = '❌ 插件初始化失败，请检查插件安装';
        } else if (oError.errorCode === 7) {
          errorMessage = '❌ 通道不存在或离线，请检查通道状态';
        } else if (oError.errorCode === 1) {
          errorMessage = '❌ 网络连接失败，请检查设备网络';
        } else {
          errorMessage = `❌ 预览失败: ${oError.errorMsg || '未知错误'} (错误码: ${oError.errorCode})`;
        }

        setStatusMessage(errorMessage);
        addLog(errorMessage, 'error');
        emit('video-error', oError);

        // 提供解决建议
        addLog('🔧 预览失败解决建议:', 'info');
        addLog('1. 检查设备是否在线', 'info');
        addLog('2. 确认通道是否存在', 'info');
        addLog('3. 检查网络连接', 'info');
        addLog('4. 尝试重新登录设备', 'info');
      }
    });
  } catch (error) {
    console.error('预览异常:', error);
    setStatusMessage('预览启动异常');
    addLog(`预览启动异常: ${error.message}`, 'error');
    emit('video-error', error);
  }
};

// 停止预览
const stopRealPlay = async () => {
  if (!WebVideoCtrl) return;

  setStatusMessage('正在停止视频预览...');

  try {
    await WebVideoCtrl.I_Stop({
      success: function () {
        console.log('停止预览成功');
        videoStatus.value.isConnected = false;
        videoStatus.value.isPlaying = false;
        setStatusMessage('视频预览已停止');
        stopConnectionCheck(); // 停止连接检测
        emit('video-disconnected');
      },
      error: function (oError) {
        console.error('停止预览失败:', oError);
        setStatusMessage('停止预览失败');
        emit('video-error', oError);
      }
    });
  } catch (error) {
    console.error('停止预览异常:', error);
    setStatusMessage('停止预览异常');
  }
};

// 切换摄像头
const switchCamera = async (cameraId) => {
  const camera = cameras.value.find(cam => cam.id === cameraId && cam.status === 'online');
  if (!camera) {
    console.error('摄像头不可用:', cameraId);
    return;
  }

  videoStatus.value.currentCamera = cameraId;

  if (videoStatus.value.isPlaying) {
    // 先停止当前预览，再开始新的预览
    await stopRealPlay();
    setTimeout(() => {
      startRealPlay();
    }, 500);
  }

  // 如果启用了实时人数统计，更新后端API的RTSP URL
  if (personCount.value.isEnabled && videoStatus.value.isPlaying) {
    setTimeout(() => {
      const rtspUrl = buildRtspUrl();
      setRtspUrlToApi(rtspUrl);
    }, 1500); // 延迟1.5秒确保视频切换完成
  }

  emit('camera-switched', cameraId);
};

// 全屏显示
const toggleFullscreen = async () => {
  if (!WebVideoCtrl) return;

  try {
    await WebVideoCtrl.I_FullScreen(true);
    addLog('切换全屏成功', 'success');
  } catch (error) {
    console.error('全屏切换失败:', error);
    addLog('全屏切换失败', 'error');
  }
};

// 刷新视频显示
const refreshVideoDisplay = async () => {
  if (!WebVideoCtrl || !videoStatus.value.isPlaying) {
    addLog('无法刷新：视频未在播放状态', 'warning');
    return;
  }

  try {
    addLog('正在刷新视频显示...', 'info');

    // 调整窗口大小
    const videoContainer = document.getElementById('videoContainer');
    if (videoContainer) {
      await WebVideoCtrl.I_Resize(videoContainer.offsetWidth, videoContainer.offsetHeight);
      addLog(`视频窗口大小已刷新: ${videoContainer.offsetWidth}x${videoContainer.offsetHeight}`, 'success');
    }

    // 检查视频容器状态
    setTimeout(() => {
      checkVideoContainerStatus();
    }, 500);

  } catch (error) {
    console.error('刷新视频显示失败:', error);
    addLog('刷新视频显示失败', 'error');
  }
};



// 连接视频（登录并开始预览）
const connectVideo = async () => {
  if (!videoStatus.value.isLoggedIn) {
    await loginDevice();
  }
  if (videoStatus.value.isLoggedIn && !videoStatus.value.isPlaying) {
    await startRealPlay();
  }
};

// 断开视频
const disconnectVideo = async () => {
  setStatusMessage('正在断开设备连接...');

  if (videoStatus.value.isPlaying) {
    await stopRealPlay();
  }

  // 停止实时人数统计
  stopPersonCountTimer();
  personCount.value.current = 0;
  personCount.value.status = 'stopped';

  if (videoStatus.value.isLoggedIn && WebVideoCtrl) {
    try {
      await WebVideoCtrl.I_Logout(deviceIdentify.value);
      videoStatus.value.isLoggedIn = false;
      videoStatus.value.isConnected = false;
      videoStatus.value.isPlaying = false;
      cameras.value = [];
      deviceIdentify.value = '';
      setStatusMessage('设备已断开连接');
      console.log('设备登出成功');
    } catch (error) {
      console.error('设备登出失败:', error);
      setStatusMessage('断开连接时发生错误');
    }
  } else {
    // 重置所有状态
    videoStatus.value.isLoggedIn = false;
    videoStatus.value.isConnected = false;
    videoStatus.value.isPlaying = false;
    cameras.value = [];
    deviceIdentify.value = '';
    setStatusMessage('请输入设备信息并登录', false); // 初始状态不自动隐藏
  }
};

// 调整视频质量
const changeQuality = (quality) => {
  videoStatus.value.quality = quality;
  emit('quality-changed', quality);
  console.log(`视频质量调整为: ${quality}`);

  // 如果正在播放，重新开始预览以应用新质量
  if (videoStatus.value.isPlaying) {
    stopRealPlay().then(() => {
      setTimeout(startRealPlay, 500);
    });
  }
};

// 暴露事件给父组件
const emit = defineEmits([
  'video-connected',
  'video-disconnected',
  'video-error',
  'camera-switched',
  'quality-changed'
]);

// 可以通过props接收配置
const props = defineProps({
  deviceIP: {
    type: String,
    default: '***********'
  },
  devicePort: {
    type: Number,
    default: 80
  },
  username: {
    type: String,
    default: 'admin'
  },
  password: {
    type: String,
    default: 'hucom12345'
  },
  protocolType: {
    type: Number,
    default: 1 // 1=HTTP, 2=HTTPS
  },
  autoConnect: {
    type: Boolean,
    default: false
  },
  showControls: {
    type: Boolean,
    default: true
  },
  defaultCamera: {
    type: Number,
    default: 1
  }
});

// 使用props初始化
if (props.deviceIP) {
  videoStatus.value.deviceIP = props.deviceIP;
}
if (props.devicePort) {
  videoStatus.value.devicePort = props.devicePort;
}
if (props.username) {
  videoStatus.value.username = props.username;
}
if (props.password) {
  videoStatus.value.password = props.password;
}
if (props.protocolType) {
  videoStatus.value.protocolType = props.protocolType;
}
if (props.defaultCamera) {
  videoStatus.value.currentCamera = props.defaultCamera;
}

// 暴露方法给父组件
defineExpose({
  connectVideo,
  disconnectVideo,
  switchCamera,
  changeQuality,
  loginDevice,
  startRealPlay,
  stopRealPlay,
  refreshVideoDisplay,
  checkVideoContainerStatus,
  getStatus: () => videoStatus.value
});

// 窗口大小调整处理
const handleResize = () => {
  if (WebVideoCtrl && pluginStatus.value.isInitialized) {
    const videoContainer = document.getElementById('videoContainer');
    if (videoContainer) {
      WebVideoCtrl.I_Resize(videoContainer.offsetWidth, videoContainer.offsetHeight);
      console.log(`视频窗口大小已调整: ${videoContainer.offsetWidth}x${videoContainer.offsetHeight}`);
    }
  }
};

// 防抖处理的窗口大小调整
const debouncedResize = (() => {
  let timeoutId = null;
  return () => {
    if (timeoutId) clearTimeout(timeoutId);
    timeoutId = setTimeout(handleResize, 100);
  };
})();

// 组件挂载
onMounted(async () => {
  addLog('VideoMonitor组件开始初始化...', 'info');

  // 添加窗口大小调整监听器
  window.addEventListener('resize', debouncedResize);

  // 初始化WebSDK
  try {
    await initWebSDK();
    addLog('WebSDK初始化完成', 'success');
  } catch (error) {
    addLog(`WebSDK初始化失败: ${error.message}`, 'error');
  }

  // 注意：自动连接逻辑已移到cbInitPluginComplete回调中
});

// 组件卸载时清理
onUnmounted(async () => {
  // 移除窗口大小调整监听器
  window.removeEventListener('resize', debouncedResize);

  // 清理定时器
  stopConnectionCheck();
  stopPersonCountTimer();

  // 清理状态消息定时器
  if (statusMessageTimer) {
    clearTimeout(statusMessageTimer);
    statusMessageTimer = null;
  }

  await disconnectVideo();

  if (WebVideoCtrl) {
    try {
      await WebVideoCtrl.I_DestroyPlugin();
      addLog('WebSDK插件销毁成功', 'success');
    } catch (error) {
      console.error('WebSDK插件销毁失败:', error);
      addLog('WebSDK插件销毁失败', 'error');
    }
  }
});
</script>

<template>
  <div class="panel-section video-monitor">
    <div class="monitor-title">
      <span style="width: 3px; height: 16px; background-color: #2997e3; margin-right: 8px;"></span>
      视频监控
      <div class="status-indicator" :class="{
        connected: videoStatus.isConnected,
        logged: videoStatus.isLoggedIn
      }"></div>
    </div>
    <div class="monitor-content">
      <!-- 设备登录配置 -->
      <div v-if="!videoStatus.isLoggedIn" class="login-config">
        <div class="config-compact">
          <input
            v-model="videoStatus.deviceIP"
            type="text"
            class="config-input-small"
            placeholder="设备IP地址"
            :disabled="videoStatus.isLogging"
            @keyup.enter="loginDevice"
          />
          <button
            v-if="!videoStatus.isLogging"
            class="login-btn-small"
            @click="loginDevice"
            :disabled="!videoStatus.deviceIP.trim()"
          >
            登录
          </button>
          <button
            v-else
            class="cancel-btn-small"
            @click="cancelLogin"
          >
            取消
          </button>
        </div>
        <div v-if="showAdvancedConfig" class="advanced-config">
          <div class="config-row-small">
            <input
              v-model.number="videoStatus.devicePort"
              type="number"
              class="config-input-small"
              placeholder="端口(默认80)"
              :disabled="videoStatus.isLogging"
            />
            <select
              v-model.number="videoStatus.protocolType"
              class="config-select-small"
              :disabled="videoStatus.isLogging"
            >
              <option value="1">HTTP</option>
              <option value="2">HTTPS</option>
            </select>
          </div>
          <div class="config-row-small">
            <input
              v-model="videoStatus.username"
              type="text"
              class="config-input-small"
              placeholder="用户名"
              :disabled="videoStatus.isLogging"
            />
            <input
              v-model="videoStatus.password"
              type="password"
              class="config-input-small"
              placeholder="密码"
              :disabled="videoStatus.isLogging"
              @keyup.enter="loginDevice"
            />
          </div>
        </div>
        <button
          class="toggle-config-btn"
          @click="showAdvancedConfig = !showAdvancedConfig"
          :disabled="videoStatus.isLogging"
        >
          {{ showAdvancedConfig ? '收起' : '高级设置' }}
        </button>
      </div>

      <!-- 插件状态提示 -->
      <div v-if="!pluginStatus.isInitialized" class="plugin-status">
        <div class="status-message status-warning">
          <i class="status-icon">🔌</i>
          插件正在初始化，请稍候...
        </div>
      </div>



      <!-- 视频显示区域 -->
      <div class="video-display" :class="{
        playing: videoStatus.isPlaying,
        logged: videoStatus.isLoggedIn,
        loading: videoStatus.isLogging
      }">
        <!-- 视频设备WebSDK视频容器 - 始终存在，通过CSS控制显示 -->
        <div id="videoContainer" class="video-container" :class="{
          'container-hidden': !videoStatus.isPlaying
        }">
          <div v-if="videoStatus.isPlaying" class="camera-info">
            <span>{{ cameras.find(cam => cam.id === videoStatus.currentCamera)?.name || '通道' + videoStatus.currentCamera }}</span>
            <span class="quality-badge">{{ videoStatus.quality }}</span>
            <span class="device-info">{{ deviceIdentify }}</span>
          </div>
          <!-- 实时人数统计显示 -->
          <div v-if="personCount.isEnabled && videoStatus.isPlaying" class="person-count-overlay">
            <div class="count-display" :class="{
              'count-active': personCount.status === 'running',
              'count-error': personCount.status === 'error',
              'count-connecting': personCount.isConnecting
            }">
              <i class="count-icon">👥</i>
              <span class="count-number">{{ personCount.current }}</span>
              <span class="count-label">人</span>
            </div>
            <div v-if="personCount.lastUpdate" class="count-time">
              {{ personCount.lastUpdate }}
            </div>
          </div>
        </div>

        <!-- 状态显示层 -->
        <div v-if="!videoStatus.isPlaying" class="status-overlay">
          <div v-if="!videoStatus.isLoggedIn && !videoStatus.isLogging" class="empty-monitor">
            <div class="no-signal">
              <i class="signal-icon">📺</i>
              <p>视频设备未连接</p>
              <p class="hint-text">请输入设备信息并登录</p>
            </div>
          </div>
          <div v-else-if="videoStatus.isLogging" class="empty-monitor">
            <div class="no-signal">
              <i class="signal-icon loading-icon">⏳</i>
              <p>正在连接设备...</p>
            </div>
          </div>
          <div v-else-if="!videoStatus.isPlaying" class="empty-monitor">
            <div class="no-signal">
              <i class="signal-icon">📹</i>
              <p>设备已连接</p>
              <p class="hint-text">{{ cameras.length > 0 ? `共${cameras.length}个通道可用` : '正在获取通道信息...' }}</p>
              <div v-if="cameras.length > 0" class="preview-controls">
                <button
                  class="connect-btn"
                  @click="startRealPlay"
                  :disabled="videoStatus.isLogging"
                >
                  {{ videoStatus.isLogging ? '连接中...' : '手动开始预览' }}
                </button>
                <p class="auto-hint">通常会自动开始预览</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 控制面板 -->
      <div v-if="props.showControls && videoStatus.isLoggedIn && cameras.length > 0" class="video-controls">
        <!-- 通道选择器 -->
        <div v-if="cameras.length > 1" class="camera-selector">
          <label class="control-label">视频通道:</label>
          <select
            v-model="videoStatus.currentCamera"
            @change="switchCamera(videoStatus.currentCamera)"
            :disabled="videoStatus.isLogging"
          >
            <option
              v-for="camera in cameras"
              :key="camera.id"
              :value="camera.id"
              :disabled="camera.status === 'offline'"
            >
              {{ camera.name }} {{ camera.status === 'offline' ? '(离线)' : '' }}
            </option>
          </select>
        </div>

        <!-- 控制按钮 -->
        <div class="control-buttons">
          <button
            class="control-btn"
            @click="videoStatus.isPlaying ? stopRealPlay() : startRealPlay()"
            :disabled="videoStatus.isLogging"
          >
            {{ videoStatus.isPlaying ? '停止预览' : '开始预览' }}
          </button>
          <button
            class="control-btn"
            @click="toggleFullscreen"
            :disabled="!videoStatus.isPlaying || videoStatus.isLogging"
            title="全屏显示视频"
          >
            全屏
          </button>
          <button
            class="control-btn refresh-btn"
            @click="refreshVideoDisplay"
            :disabled="!videoStatus.isPlaying || videoStatus.isLogging"
            title="刷新视频显示"
          >
            刷新
          </button>
        </div>

        <!-- 状态提示信息 -->
        <div v-if="videoStatus.showStatusMessage" class="status-message" :class="{
          'status-error': videoStatus.statusMessage.includes('失败') || videoStatus.statusMessage.includes('错误'),
          'status-success': videoStatus.statusMessage.includes('成功') || videoStatus.statusMessage.includes('正在播放'),
          'status-warning': videoStatus.statusMessage.includes('正在') && !videoStatus.statusMessage.includes('正在播放')
        }">
          <i class="status-icon">
            {{ videoStatus.isLogging ? '⏳' :
               videoStatus.statusMessage.includes('失败') || videoStatus.statusMessage.includes('错误') ? '❌' :
               videoStatus.statusMessage.includes('成功') || videoStatus.statusMessage.includes('正在播放') ? '✅' : 'ℹ️' }}
          </i>
          {{ videoStatus.statusMessage }}
        </div>

        <!-- 实时人数统计控制 -->
        <div class="person-count-section">
          <div class="count-control-header">
            <label class="control-label">实时人数统计:</label>
            <button
              class="toggle-count-btn"
              @click="togglePersonCount"
              :disabled="videoStatus.isLogging"
              :class="{ active: personCount.isEnabled }"
            >
              {{ personCount.isEnabled ? '已启用' : '已禁用' }}
            </button>
          </div>
          <div v-if="personCount.isEnabled" class="count-status">
            <div class="count-info">
              <span class="count-current">当前人数: {{ personCount.current }}</span>
              <span class="count-status-text" :class="{
                'status-running': personCount.status === 'running',
                'status-error': personCount.status === 'error',
                'status-connecting': personCount.isConnecting
              }">
                {{ personCount.isConnecting ? '连接中...' :
                   personCount.status === 'running' ? '识别中' :
                   personCount.status === 'error' ? '连接失败' : '已停止' }}
              </span>
            </div>
            <div v-if="personCount.lastUpdate" class="count-update-time">
              最后更新: {{ personCount.lastUpdate }}
            </div>
          </div>
        </div>

        <!-- 断开连接按钮 -->
        <div class="disconnect-section">
          <button
            class="control-btn disconnect-btn"
            @click="disconnectVideo"
            :disabled="videoStatus.isLogging"
          >
            断开设备
          </button>
        </div>
      </div>

      <!-- 操作日志 -->
      <div v-if="operationLogs.length > 0" class="operation-logs">
        <div class="logs-header">
          <span class="logs-title">操作日志</span>
          <button class="clear-logs-btn" @click="clearLogs">清空</button>
        </div>
        <div class="logs-content">
          <div
            v-for="log in operationLogs.slice(0, 5)"
            :key="log.id"
            class="log-item"
            :class="`log-${log.type}`"
          >
            <span class="log-time">{{ log.timestamp }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>

      <!-- 插件安装提示 -->
      <div v-if="!pluginStatus.isInstalled && operationLogs.some(log => log.message.includes('插件未安装'))" class="plugin-install-tip">
        <div class="tip-header">
          <i class="tip-icon">🔧</i>
          <span>需要安装视频插件</span>
        </div>
        <div class="tip-content">
          <p>视频监控功能需要安装浏览器插件才能正常使用</p>
          <div class="tip-actions">
            <a href="/hikvision/HCWebSDKPluginsUserSetup.exe" download class="download-btn">
              下载插件安装包
            </a>
            <button class="retry-btn" @click="initWebSDK">
              重新检测
            </button>
          </div>
          <div class="tip-note">
            <small>下载后请运行安装程序，然后刷新页面</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 面板通用样式 */
.panel-section {
  margin-bottom: 5px;
  border: 1px solid #1e3a5f;
  border-radius: 4px;
  background-color: #0d2b55;
}

.monitor-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: bold;
  color: white;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ff4d4f;
  transition: background-color 0.3s ease;
}

.status-indicator.logged {
  background-color: #faad14;
}

.status-indicator.connected {
  background-color: #52c41a;
}

/* 视频监控样式 */
.monitor-content {
  padding: 10px;
}

/* 登录配置样式 */
.login-config {
  margin-bottom: 8px;
  padding: 6px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.config-compact {
  display: flex;
  gap: 4px;
  align-items: center;
  margin-bottom: 4px;
}

.config-input-small {
  flex: 1;
  padding: 3px 6px;
  background-color: #1e3a5f;
  color: white;
  border: 1px solid #2997e3;
  border-radius: 3px;
  font-size: 0.75rem;
  min-width: 0;
}

.config-input-small:focus {
  outline: none;
  border-color: #40a9ff;
}

.config-input-small::placeholder {
  color: #8c8c8c;
  font-size: 0.7rem;
}

.login-btn-small {
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.75rem;
  white-space: nowrap;
}

.login-btn-small:hover {
  background-color: #40a9ff;
}

.cancel-btn-small {
  background-color: #ff4d4f;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.75rem;
  white-space: nowrap;
}

.cancel-btn-small:hover {
  background-color: #ff7875;
}

.advanced-config {
  margin-top: 4px;
  animation: slideDown 0.3s ease;
}

.config-row-small {
  display: flex;
  gap: 4px;
  margin-bottom: 4px;
}

.toggle-config-btn {
  background-color: transparent;
  color: #8ebbff;
  border: 1px solid #2997e3;
  padding: 2px 6px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.7rem;
  width: 100%;
  margin-top: 4px;
}

.toggle-config-btn:hover {
  background-color: rgba(41, 151, 227, 0.1);
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 100px;
  }
}

/* 状态提示样式 */
.status-message {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  margin-bottom: 8px;
  background-color: rgba(24, 144, 255, 0.1);
  border: 1px solid #1890ff;
  border-radius: 4px;
  font-size: 0.75rem;
  color: #8ebbff;
  transition: all 0.3s ease;
}

.status-message.status-error {
  background-color: rgba(255, 77, 79, 0.1);
  border-color: #ff4d4f;
  color: #ff7875;
}

.status-message.status-success {
  background-color: rgba(82, 196, 26, 0.1);
  border-color: #52c41a;
  color: #73d13d;
}

.status-message.status-warning {
  background-color: rgba(250, 173, 20, 0.1);
  border-color: #faad14;
  color: #ffc53d;
}

.status-icon {
  font-size: 0.8rem;
  flex-shrink: 0;
}

/* 加载状态样式 */
.video-display.loading {
  border-color: #faad14;
}

.loading-icon {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.hint-text {
  font-size: 0.7rem;
  color: #666;
  margin-top: 4px;
}

.preview-controls {
  margin-top: 8px;
}

.auto-hint {
  font-size: 0.65rem;
  color: #888;
  margin-top: 4px;
  font-style: italic;
}

/* 控制面板优化 */
.control-label {
  font-size: 0.75rem;
  color: #8ebbff;
  margin-right: 6px;
}

.camera-selector {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.camera-selector select {
  flex: 1;
  margin-left: 6px;
}

/* 实时人数统计控制面板样式 */
.person-count-section {
  margin-top: 8px;
  padding: 8px;
  border: 1px solid #1e3a5f;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.1);
}

.count-control-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
}

.toggle-count-btn {
  background-color: #666;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.75rem;
  transition: all 0.3s ease;
}

.toggle-count-btn.active {
  background-color: #52c41a;
}

.toggle-count-btn:hover:not(:disabled) {
  opacity: 0.8;
}

.toggle-count-btn:disabled {
  background-color: #444;
  cursor: not-allowed;
  opacity: 0.5;
}

.count-status {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.count-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.count-current {
  color: #8ebbff;
  font-weight: bold;
}

.count-status-text {
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 3px;
}

.status-running {
  background-color: rgba(82, 196, 26, 0.2);
  color: #73d13d;
}

.status-error {
  background-color: rgba(255, 77, 79, 0.2);
  color: #ff7875;
}

.status-connecting {
  background-color: rgba(250, 173, 20, 0.2);
  color: #ffc53d;
}

.count-update-time {
  font-size: 0.65rem;
  color: #666;
  text-align: right;
}

.disconnect-section {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #1e3a5f;
}

.control-btn:disabled {
  background-color: #666;
  cursor: not-allowed;
  opacity: 0.5;
}

.config-input-small:disabled {
  background-color: #333;
  color: #666;
  cursor: not-allowed;
}

.config-select-small {
  flex: 1;
  padding: 3px 6px;
  background-color: #1e3a5f;
  color: white;
  border: 1px solid #2997e3;
  border-radius: 3px;
  font-size: 0.75rem;
  min-width: 0;
}

.config-select-small:focus {
  outline: none;
  border-color: #40a9ff;
}

.config-select-small:disabled {
  background-color: #333;
  color: #666;
  cursor: not-allowed;
}

.login-btn-small:disabled {
  background-color: #666;
  cursor: not-allowed;
  opacity: 0.6;
}

.toggle-config-btn:disabled {
  color: #666;
  border-color: #666;
  cursor: not-allowed;
}

.video-display {
  width: 100%;
  height: 180px;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px dashed #1e3a5f;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  overflow: hidden;
  position: relative;
}

.video-display.logged {
  border-color: #faad14;
}

.video-display.playing {
  border: 1px solid #52c41a;
}

.empty-monitor {
  text-align: center;
  color: #8c8c8c;
}

.no-signal {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.signal-icon {
  font-size: 2rem;
  opacity: 0.5;
}

.connect-btn {
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.connect-btn:hover {
  background-color: #40a9ff;
}

.video-stream {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 视频设备容器样式 */
.video-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: #000;
  overflow: hidden;
  z-index: 1;
}

/* 当视频未播放时隐藏容器内容但保持DOM结构 */
.video-container.container-hidden {
  visibility: hidden;
  pointer-events: none;
}

/* 状态显示层 */
.status-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 确保插件对象能正确显示 */
.video-container object,
.video-container embed {
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 1;
}

/* 视频插件容器的特殊样式 */
.video-container > div:first-child {
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
}

.camera-info {
  position: absolute;
  top: 8px;
  left: 8px;
  display: flex;
  gap: 6px;
  z-index: 10;
}

.camera-info span {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.7rem;
}

.quality-badge {
  background-color: #52c41a !important;
}

.device-info {
  background-color: #1890ff !important;
}

/* 实时人数统计覆盖层样式 */
.person-count-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 15;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.count-display {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 10px;
  border-radius: 6px;
  font-weight: bold;
  border: 2px solid #666;
  transition: all 0.3s ease;
}

.count-display.count-active {
  border-color: #52c41a;
  background-color: rgba(82, 196, 26, 0.2);
  color: #73d13d;
}

.count-display.count-error {
  border-color: #ff4d4f;
  background-color: rgba(255, 77, 79, 0.2);
  color: #ff7875;
}

.count-display.count-connecting {
  border-color: #faad14;
  background-color: rgba(250, 173, 20, 0.2);
  color: #ffc53d;
}

.count-icon {
  font-size: 1rem;
}

.count-number {
  font-size: 1.2rem;
  font-weight: bold;
  min-width: 20px;
  text-align: center;
}

.count-label {
  font-size: 0.8rem;
}

.count-time {
  background-color: rgba(0, 0, 0, 0.6);
  color: #ccc;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.65rem;
}

.video-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.camera-selector select,
.quality-selector select {
  width: 100%;
  padding: 4px 8px;
  background-color: #1e3a5f;
  color: white;
  border: 1px solid #2997e3;
  border-radius: 3px;
  font-size: 0.8rem;
}

.camera-selector select:focus,
.quality-selector select:focus {
  outline: none;
  border-color: #40a9ff;
}

.control-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.control-btn {
  flex: 1;
  min-width: 80px;
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 8px 6px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.75rem;
  transition: all 0.3s ease;
}

.control-btn:hover:not(:disabled) {
  background-color: #40a9ff;
}

.control-btn:disabled {
  background-color: #666;
  cursor: not-allowed;
  opacity: 0.6;
}

.disconnect-btn {
  background-color: #ff4d4f;
}

.disconnect-btn:hover:not(:disabled) {
  background-color: #ff7875;
}

.refresh-btn {
  background-color: #52c41a;
}

.refresh-btn:hover:not(:disabled) {
  background-color: #73d13d;
}

/* 操作日志样式 */
.operation-logs {
  margin-top: 8px;
  border: 1px solid #1e3a5f;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.2);
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  border-bottom: 1px solid #1e3a5f;
}

.logs-title {
  font-size: 0.75rem;
  color: #8ebbff;
  font-weight: bold;
}

.clear-logs-btn {
  background-color: transparent;
  color: #666;
  border: 1px solid #666;
  padding: 2px 6px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.65rem;
}

.clear-logs-btn:hover {
  color: #8ebbff;
  border-color: #8ebbff;
}

.logs-content {
  max-height: 100px;
  overflow-y: auto;
  padding: 4px;
}

.log-item {
  display: flex;
  gap: 6px;
  padding: 2px 4px;
  margin-bottom: 2px;
  border-radius: 2px;
  font-size: 0.65rem;
}

.log-time {
  color: #666;
  min-width: 60px;
  flex-shrink: 0;
}

.log-message {
  flex: 1;
  word-break: break-word;
}

.log-info {
  color: #8ebbff;
}

.log-success {
  color: #73d13d;
}

.log-error {
  color: #ff7875;
}

.log-warning {
  color: #ffc53d;
}

/* 插件安装提示样式 */
.plugin-install-tip {
  margin-top: 8px;
  border: 1px solid #faad14;
  border-radius: 4px;
  background-color: rgba(250, 173, 20, 0.1);
  padding: 8px;
}

.tip-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
  font-weight: bold;
  color: #ffc53d;
  font-size: 0.8rem;
}

.tip-icon {
  font-size: 1rem;
}

.tip-content p {
  margin: 0 0 8px 0;
  font-size: 0.75rem;
  color: #ccc;
}

.tip-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 6px;
}

.download-btn {
  background-color: #1890ff;
  color: white;
  text-decoration: none;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 0.75rem;
  transition: background-color 0.3s;
}

.download-btn:hover {
  background-color: #40a9ff;
}

.retry-btn {
  background-color: #52c41a;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.75rem;
  transition: background-color 0.3s;
}

.retry-btn:hover {
  background-color: #73d13d;
}

.tip-note {
  color: #888;
  font-size: 0.65rem;
  font-style: italic;
}

.plugin-status {
  margin-bottom: 8px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .video-display {
    height: 120px;
  }

  .control-buttons {
    flex-direction: column;
  }

  .control-btn {
    flex: none;
  }
}
</style>
