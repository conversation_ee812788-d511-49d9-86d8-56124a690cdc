from flask import Flask, jsonify
from threading import Thread
import cv2
from ultralytics import YOLO
import torch
import time
from flask_cors import CORS
import os
import csv
from datetime import datetime

app = Flask(__name__)
CORS(app)  # 启用跨域支持

current_person_count = 0

# 保存CSV路径和文件夹
csv_dir = r"C:\Users\<USER>\Desktop\yolov8_count\count"
os.makedirs(csv_dir, exist_ok=True)

def get_today_csv_path():
    today_str = datetime.now().strftime("%Y-%m-%d")
    return os.path.join(csv_dir, f"{today_str}.csv")

# 初始化当天CSV文件，写入表头（如果文件不存在）
if not os.path.exists(get_today_csv_path()):
    with open(get_today_csv_path(), 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['Time', 'Count'])

def save_count_to_csv(time_str, count):
    csv_path = get_today_csv_path()
    with open(csv_path, 'a', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow([time_str, count])

@app.route("/api/count")
def get_count():
    return jsonify({"count": current_person_count})

def show_frame_with_count(frame, count, width=640, height=480):
    cv2.namedWindow('YOLO Person Detection', cv2.WINDOW_NORMAL)
    cv2.resizeWindow('YOLO Person Detection', width, height)

    resized_frame = cv2.resize(frame, (width, height))
    cv2.putText(resized_frame, f'Persons: {count}', (20, 50),
                cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 255, 0), 3)
    cv2.imshow('YOLO Person Detection', resized_frame)

def detection_loop():
    global current_person_count
    model = YOLO('1000time.pt')
    model.to('cuda' if torch.cuda.is_available() else 'cpu')

    url = 'rtsp://admin:hucom12345@10.10.52.37/h264/ch47/main/av_stream'
    cap = cv2.VideoCapture(url)

    frame_count = 0
    last_count = 0

    show_window = True  # 【控制窗口显示，改 False 不显示】

    while True:
        success, frame = cap.read()
        if not success:
            print("视频帧读取失败，尝试重连RTSP...")
            time.sleep(3)
            cap.release()
            cap = cv2.VideoCapture(url)
            continue

        frame_count += 1

        if frame_count == 1 or frame_count == 15:
            results = model.predict(frame, classes=[0])
            boxes = results[0].boxes.xyxy
            person_count = len(boxes)
            current_person_count = person_count
            last_count = person_count

            annotated_frame = results[0].plot()
            display_frame = annotated_frame
        else:
            display_frame = frame

        if show_window:
            show_frame_with_count(display_frame, last_count, 1200, 800)

        frame_count = frame_count % 30

        if show_window and cv2.waitKey(1) & 0xFF == ord('q'):
            break

        # 保存检测时间和人数
        now_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        save_count_to_csv(now_time, last_count)

    cap.release()
    if show_window:
        cv2.destroyAllWindows()

if __name__ == '__main__':
    Thread(target=detection_loop, daemon=True).start()
    app.run(host='0.0.0.0', port=5000)
